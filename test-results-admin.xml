<testsuites id="" name="" tests="56" failures="2" skipped="0" errors="0" time="120.58628999999999">
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="chromium" tests="8" failures="0" skipped="0" time="39.613" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.153">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="7.268">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="4.088">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="2.512">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="2.648">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="8.552">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="6.386">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="6.006">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="firefox" tests="8" failures="0" skipped="0" time="53.513" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="3.071">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="12.42">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="5.63">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="6.342">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="3.028">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="9.227">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="6.797">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="6.998">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="webkit" tests="8" failures="2" skipped="0" time="64.631" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="1.794">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="8.721">
<failure message="admin-auth.spec.ts:25:6 should login successfully with valid admin credentials" type="FAILURE">
<![CDATA[  [webkit] › admin-auth.spec.ts:25:6 › Admin Authentication › should login successfully with valid admin credentials 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      52 |
      53 | 		// Should show either content or error state
    > 54 | 		expect(hasContent || hasError || hasTryAgain).toBe(true);
         | 		                                              ^
      55 |
      56 | 		// Check navigation sidebar
      57 | 		await expect(page.locator('text=Admin Panel')).toBeVisible();
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="5.678">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="3.752">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="3.802">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="31.541">
<failure message="admin-auth.spec.ts:110:6 should logout successfully" type="FAILURE">
<![CDATA[  [webkit] › admin-auth.spec.ts:110:6 › Admin Authentication › should logout successfully ──────────

    Test timeout of 30000ms exceeded.

    Error: page.waitForURL: Test timeout of 30000ms exceeded.
    =========================== logs ===========================
    waiting for navigation to "/admin" until "load"
    ============================================================

      113 | 		await page.fill('input[id="password"]', 'admin123');
      114 | 		await page.click('button[type="submit"]');
    > 115 | 		await page.waitForURL('/admin');
          | 		           ^
      116 |
      117 | 		// Try to open mobile sidebar if menu button exists and is visible
      118 | 		try {
        at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:115:14

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../../test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/test-failed-1.png]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/video.webm]]

[[ATTACHMENT|test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="5.019">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="4.324">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="Mobile Chrome" tests="8" failures="0" skipped="0" time="53.121" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.172">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="8.065">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="5.195">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="3.791">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="2.418">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="20.422">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="5.338">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="5.72">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="Mobile Safari" tests="8" failures="0" skipped="0" time="45.55" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.216">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="6.598">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="3.338">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="1.749">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="2.59">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="19.846">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="3.665">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="5.548">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="Microsoft Edge" tests="8" failures="0" skipped="0" time="43.448" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.664">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="8.396">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="5.113">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="2.95">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="2.532">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="8.921">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="6.041">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="6.831">
</testcase>
</testsuite>
<testsuite name="admin-auth.spec.ts" timestamp="2025-07-21T16:22:03.058Z" hostname="Google Chrome" tests="8" failures="0" skipped="0" time="60.163" errors="0">
<testcase name="Admin Authentication › should display admin login page correctly" classname="admin-auth.spec.ts" time="2.572">
</testcase>
<testcase name="Admin Authentication › should login successfully with valid admin credentials" classname="admin-auth.spec.ts" time="14.346">
</testcase>
<testcase name="Admin Authentication › should show error for invalid credentials" classname="admin-auth.spec.ts" time="4.734">
</testcase>
<testcase name="Admin Authentication › should show validation errors for empty fields" classname="admin-auth.spec.ts" time="4.564">
</testcase>
<testcase name="Admin Authentication › should toggle password visibility" classname="admin-auth.spec.ts" time="1.628">
</testcase>
<testcase name="Admin Authentication › should logout successfully" classname="admin-auth.spec.ts" time="13.612">
</testcase>
<testcase name="Admin Authentication › should redirect to login when accessing protected route without auth" classname="admin-auth.spec.ts" time="9.25">
</testcase>
<testcase name="Admin Authentication › should handle loading states during login" classname="admin-auth.spec.ts" time="9.457">
</testcase>
</testsuite>
</testsuites>