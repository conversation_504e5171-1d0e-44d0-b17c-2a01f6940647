import { AuditLog, Prisma } from '@prisma/client';
import { BaseRepository } from './base.repository';
import { AuditEvent, AuditLogQuery } from '@/backend/services/audit.service';

export interface AuditRepository extends BaseRepository<AuditLog> {
	findAuditLogs(query: AuditLogQuery): Promise<AuditLog[]>;
	countAuditLogs(query: Omit<AuditLogQuery, 'limit' | 'offset'>): Promise<number>;
	findByUserId(userId: string, limit?: number): Promise<AuditLog[]>;
	findByAdminId(adminId: string, limit?: number): Promise<AuditLog[]>;
	findByResource(resource: string, resourceId?: string, limit?: number): Promise<AuditLog[]>;
	deleteOldLogs(olderThan: Date): Promise<number>;
}

export class AuditRepositoryImpl implements AuditRepository {
	constructor(private readonly prisma: any) {}

	async findById(id: string): Promise<AuditLog | null> {
		return this.prisma.auditLog.findUnique({
			where: { id },
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
		});
	}

	async findOne(where: Prisma.AuditLogWhereInput): Promise<AuditLog | null> {
		return this.prisma.auditLog.findFirst({
			where,
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
		});
	}

	async find(where: Prisma.AuditLogWhereInput): Promise<AuditLog[]> {
		return this.prisma.auditLog.findMany({
			where,
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
			orderBy: {
				timestamp: 'desc',
			},
		});
	}

	async create(data: Prisma.AuditLogCreateInput): Promise<AuditLog> {
		return this.prisma.auditLog.create({
			data,
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
		});
	}

	async update(id: string, data: Prisma.AuditLogUpdateInput): Promise<AuditLog> {
		return this.prisma.auditLog.update({
			where: { id },
			data,
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
		});
	}

	async delete(id: string): Promise<AuditLog> {
		return this.prisma.auditLog.delete({
			where: { id },
		});
	}

	async findAuditLogs(query: AuditLogQuery): Promise<AuditLog[]> {
		const where: Prisma.AuditLogWhereInput = {};

		if (query.action) {
			where.action = query.action;
		}

		if (query.resource) {
			where.resource = query.resource;
		}

		if (query.user_id) {
			where.user_id = query.user_id;
		}

		if (query.admin_id) {
			where.admin_id = query.admin_id;
		}

		if (query.start_date || query.end_date) {
			where.timestamp = {};
			if (query.start_date) {
				where.timestamp.gte = query.start_date;
			}
			if (query.end_date) {
				where.timestamp.lte = query.end_date;
			}
		}

		return this.prisma.auditLog.findMany({
			where,
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
			orderBy: {
				timestamp: 'desc',
			},
			take: query.limit || 50,
			skip: query.offset || 0,
		});
	}

	async countAuditLogs(query: Omit<AuditLogQuery, 'limit' | 'offset'>): Promise<number> {
		const where: Prisma.AuditLogWhereInput = {};

		if (query.action) {
			where.action = query.action;
		}

		if (query.resource) {
			where.resource = query.resource;
		}

		if (query.user_id) {
			where.user_id = query.user_id;
		}

		if (query.admin_id) {
			where.admin_id = query.admin_id;
		}

		if (query.start_date || query.end_date) {
			where.timestamp = {};
			if (query.start_date) {
				where.timestamp.gte = query.start_date;
			}
			if (query.end_date) {
				where.timestamp.lte = query.end_date;
			}
		}

		return this.prisma.auditLog.count({ where });
	}

	async findByUserId(userId: string, limit: number = 50): Promise<AuditLog[]> {
		return this.prisma.auditLog.findMany({
			where: { user_id: userId },
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
			orderBy: {
				timestamp: 'desc',
			},
			take: limit,
		});
	}

	async findByAdminId(adminId: string, limit: number = 50): Promise<AuditLog[]> {
		return this.prisma.auditLog.findMany({
			where: { admin_id: adminId },
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
			orderBy: {
				timestamp: 'desc',
			},
			take: limit,
		});
	}

	async findByResource(resource: string, resourceId?: string, limit: number = 50): Promise<AuditLog[]> {
		const where: Prisma.AuditLogWhereInput = { resource };
		
		if (resourceId) {
			where.resource_id = resourceId;
		}

		return this.prisma.auditLog.findMany({
			where,
			include: {
				user: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
				admin: {
					select: {
						id: true,
						username: true,
						role: true,
					},
				},
			},
			orderBy: {
				timestamp: 'desc',
			},
			take: limit,
		});
	}

	async deleteOldLogs(olderThan: Date): Promise<number> {
		const result = await this.prisma.auditLog.deleteMany({
			where: {
				timestamp: {
					lt: olderThan,
				},
			},
		});

		return result.count;
	}
}
