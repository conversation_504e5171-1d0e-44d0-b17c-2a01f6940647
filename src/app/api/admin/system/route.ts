import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/api-error-middleware';
import { withAdminAuth } from '@/backend/middleware/admin.middleware';
import { getAdminService } from '@/backend/wire';
import { z } from 'zod';

// Validation schemas
const clearCacheSchema = z.object({
	cacheType: z.string().optional(),
});

/**
 * GET /api/admin/system
 * Get system health and monitoring information
 */
async function GET(request: NextRequest) {
	try {
		const adminService = getAdminService();

		const systemHealth = await adminService.getSystemHealth();
		const tokenUsageStats = await adminService.getTokenUsageStats();
		const errorLogs = await adminService.getErrorLogs(20);

		return NextResponse.json({
			success: true,
			data: {
				health: systemHealth,
				tokenUsage: tokenUsageStats,
				recentErrors: errorLogs,
			},
		});
	} catch (error) {
		console.error('Error fetching system information:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to fetch system information',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

/**
 * POST /api/admin/system/clear-cache
 * Clear system cache
 */
async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const { cacheType } = clearCacheSchema.parse(body);

		// Get admin user info from middleware
		const adminUser = (request as any).user;
		const ipAddress = request.headers.get('x-forwarded-for') ||
						  request.headers.get('x-real-ip') ||
						  'unknown';
		const userAgent = request.headers.get('user-agent') || 'unknown';

		const adminService = getAdminService();
		const result = await adminService.clearCache(cacheType, adminUser?.id, ipAddress, userAgent);

		return NextResponse.json({
			success: result.success,
			message: result.message,
		});
	} catch (error) {
		console.error('Error clearing cache:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{ status: 400 }
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to clear cache',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Apply admin authentication middleware
const wrappedGET = withAdminAuth(withErrorHandling(GET));
const wrappedPOST = withAdminAuth(withErrorHandling(POST));

export { wrappedGET as GET, wrappedPOST as POST };
