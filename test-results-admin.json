{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.admin.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e/admin", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/admin/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/admin/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report-admin"}], ["json", {"outputFile": "test-results-admin.json"}], ["junit", {"outputFile": "test-results-admin.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/test-results-admin", "repeatEach": 1, "retries": 0, "metadata": {"testType": "admin-dashboard", "version": "1.0.0", "description": "Comprehensive admin dashboard functionality tests", "actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e/admin", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [{"title": "admin-auth.spec.ts", "file": "admin-auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Au<PERSON>ntication", "file": "admin-auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2153, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:04.084Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-e07b528e098b32590633", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 7268, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:04.119Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-2f4faaa127380714a7fc", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 4088, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:04.079Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-faa3c86516706f823282", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 2512, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:04.100Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-b5bc2839db4808544af5", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2648, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:06.590Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ccd258b5471eba1ecec0", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 8552, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:06.937Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4f07e2b40cbcda0e2c3a", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6386, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:08.502Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-827a1f1084f083e154a5", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 6006, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:09.250Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-d5f440fcfc49ad1b2df0", "file": "admin-auth.spec.ts", "line": 161, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 3071, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:14.300Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de90acda1fefc2217361", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 2, "status": "passed", "duration": 12420, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:16.159Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-d1cd827dff44d9751e85", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "passed", "duration": 5630, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:16.262Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-da6b8ab7c64595c4e01d", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 3, "status": "passed", "duration": 6342, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:16.275Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-927e1d55fcab8d60e7ce", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 3028, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:19.706Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-a608d34dcc5c6a1eeba1", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "passed", "duration": 9227, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:22.686Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-8a13156c56b46d2b43fd", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 6797, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:22.746Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0e8b4d0c5c71d74a4890", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 3, "status": "passed", "duration": 6998, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:24.547Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-423b58a9bafab961e43f", "file": "admin-auth.spec.ts", "line": 161, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 1794, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:31.987Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-6c141e6d2620b9f23943", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 3, "status": "failed", "duration": 8721, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49", "location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}, "snippet": "\u001b[0m \u001b[90m 52 |\u001b[39m\n \u001b[90m 53 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 55 |\u001b[39m\n \u001b[90m 56 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 57 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 52 |\u001b[39m\n \u001b[90m 53 |\u001b[39m \t\t\u001b[90m// Should show either content or error state\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m \t\texpect(hasContent \u001b[33m||\u001b[39m hasError \u001b[33m||\u001b[39m hasTryAgain)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m \t\t                                              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 55 |\u001b[39m\n \u001b[90m 56 |\u001b[39m \t\t\u001b[90m// Check navigation sidebar\u001b[39m\n \u001b[90m 57 |\u001b[39m \t\t\u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Admin Panel'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:54:49\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:33.190Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentic-4e32c-ith-valid-admin-credentials-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 49, "line": 54}}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-9e0f96edac4fe93a188e", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "passed", "duration": 5678, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:33.833Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-946aa1d11b1ac7da9b12", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 2, "status": "passed", "duration": 3752, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:34.142Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-92eff4f021ca8086fe26", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "passed", "duration": 3802, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:34.260Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4bf2c60d2006eb42b18d", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 1, "status": "timedOut", "duration": 31541, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts", "column": 14, "line": 115}, "message": "Error: page.waitForURL: Test timeout of 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation to \"/admin\" until \"load\"\n============================================================\n\n\u001b[0m \u001b[90m 113 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[id=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'admin123'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 115 |\u001b[39m \t\t\u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForURL(\u001b[32m'/admin'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m \t\t           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 116 |\u001b[39m\n \u001b[90m 117 |\u001b[39m \t\t\u001b[90m// Try to open mobile sidebar if menu button exists and is visible\u001b[39m\n \u001b[90m 118 |\u001b[39m \t\t\u001b[36mtry\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/admin/admin-auth.spec.ts:115:14\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:38.092Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/test-results-admin/admin-auth-Admin-Authentication-should-logout-successfully-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "4030f0bfef91ae700c6b-5f8b7254028e1cc22a0d", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 2, "status": "passed", "duration": 5019, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:38.718Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-36b3214bfb3a96d7efa3", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "passed", "duration": 4324, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:39.961Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-cf1c1ae8c58245773f6d", "file": "admin-auth.spec.ts", "line": 161, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "passed", "duration": 2172, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:44.092Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de3da8db9b428459a00e", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 2, "status": "passed", "duration": 8065, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:44.847Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ee6050b5b4dae47fe573", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "passed", "duration": 5195, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:46.262Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-f54ead5b5bbe9aed4077", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "passed", "duration": 3791, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:46.912Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-347db03dd4cb5c642fa5", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "passed", "duration": 2418, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:50.729Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-c1b055c52f721ff9755b", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "passed", "duration": 20422, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:52.270Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-9693ee61f298a0c69fdc", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 3, "status": "passed", "duration": 5338, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:53.214Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-de79b0a7bd57e65ee203", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 13, "parallelIndex": 2, "status": "passed", "duration": 5720, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:22:53.610Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-071f0a98c1e0d14e44b8", "file": "admin-auth.spec.ts", "line": 161, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 2216, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:00.214Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-30c42275ce7aceeacbdf", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "passed", "duration": 6598, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:00.311Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-8d5318867d4f182248c8", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 3338, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:02.723Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-859929da8ed40aec96c2", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 1749, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:06.071Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-da797515f605e39b1768", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "passed", "duration": 2590, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:07.124Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-73e6cf7764f00300f993", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 15, "parallelIndex": 3, "status": "passed", "duration": 19846, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:07.830Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-48ae075c7ef68f005e0e", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 17, "parallelIndex": 1, "status": "passed", "duration": 3665, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:12.819Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4eb8598866cefa871fe4", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "passed", "duration": 5548, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:09.801Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-3ab414b93b2cd6be4e8f", "file": "admin-auth.spec.ts", "line": 161, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "passed", "duration": 2664, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:14.763Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-72c3b507879d2e78ad0b", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 8396, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:17.243Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5745c84357d881b33ece", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "passed", "duration": 5113, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:18.744Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-83efad3e06fe673a39d4", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "passed", "duration": 2950, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:20.205Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0a4064ec9bb8fec4fe35", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "passed", "duration": 2532, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:23.198Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-fe163bd22b613efc5565", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 20, "parallelIndex": 1, "status": "passed", "duration": 8921, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:24.950Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-48e309fbb1066f5351ed", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "passed", "duration": 6041, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:25.745Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-991373299bbbd2ab38e8", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Microsoft Edge", "projectName": "Microsoft Edge", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "passed", "duration": 6831, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:26.266Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-4f2ee0eb70d6d5d59a2a", "file": "admin-auth.spec.ts", "line": 161, "column": 6}, {"title": "should display admin login page correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "passed", "duration": 2572, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:30.225Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5c5b86a93858bff814b6", "file": "admin-auth.spec.ts", "line": 9, "column": 6}, {"title": "should login successfully with valid admin credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 24, "parallelIndex": 0, "status": "passed", "duration": 14346, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:36.708Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-0628ad6ce4554ab37ea8", "file": "admin-auth.spec.ts", "line": 25, "column": 6}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "passed", "duration": 4734, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:34.985Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-29106df1bbb1b1dabcd8", "file": "admin-auth.spec.ts", "line": 61, "column": 6}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "passed", "duration": 4564, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:35.365Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-fe558859a06439fe946e", "file": "admin-auth.spec.ts", "line": 78, "column": 6}, {"title": "should toggle password visibility", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "passed", "duration": 1628, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:33.958Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-6e8be48d9fd5d4d3b34e", "file": "admin-auth.spec.ts", "line": 90, "column": 6}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "passed", "duration": 13612, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:35.594Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-085817e09d2cc5767a8a", "file": "admin-auth.spec.ts", "line": 110, "column": 6}, {"title": "should redirect to login when accessing protected route without auth", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "passed", "duration": 9250, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:40.236Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-ccc7711631484b066f5b", "file": "admin-auth.spec.ts", "line": 150, "column": 6}, {"title": "should handle loading states during login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Google Chrome", "projectName": "Google Chrome", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "passed", "duration": 9457, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-21T16:23:40.723Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4030f0bfef91ae700c6b-5ab003898c15cef14490", "file": "admin-auth.spec.ts", "line": 161, "column": 6}]}]}], "errors": [], "stats": {"startTime": "2025-07-21T16:21:51.363Z", "duration": 120586.29, "expected": 54, "skipped": 0, "unexpected": 2, "flaky": 0}}